import {CDN_URL_FOR_IMAGES} from "../utils/constants";

const BodyCard = (props) => {
    const { cloudinaryImageId,costForTwoMessage,avgRating,cuisines,name } = props?.resData?.card?.card?.info;
    // console.log(info);
    // console.log(props);
    
    return (
        <div className = "body-card">
            <img src = {CDN_URL_FOR_IMAGES+cloudinaryImageId}></img>
            <div className = "card-details">
                <h2>{name}</h2>
                <h3>{cuisines.join(", ")}</h3>
                <h4>{avgRating} stars</h4>
                <h4>{costForTwoMessage}</h4>

            </div>
        </div>
    );
}

export default BodyCard;
