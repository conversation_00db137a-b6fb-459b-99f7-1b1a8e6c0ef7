import RestaurantCard from "./RestaurantCard";
import Shimmer from "./Shimmer";
import {useState,useEffect} from "react";
export const Body = () => {
  const [restaurants, setRestaurants] = useState([])
  const [filteredRestaurants, setFilteredRestaurants] = useState([])
  const [searchText, setSearchText] = useState("")
  useEffect (() => {
    fetchData();
  },[]);
  const fetchData =async () => {
    const data = await fetch('https://www.swiggy.com/dapi/restaurants/list/v5?lat=12.9754605&lng=80.2207047&collection=142465&tags=&sortBy=&filters=&type=rcv2&offset=0&page_type=null')
    const restaurantsList = await data.json();
    setRestaurants(restaurantsList?.data?.cards?.slice(2));
    setFilteredRestaurants(restaurantsList?.data?.cards?.slice(2));
  }
  console.log('Body rendered')
  return restaurants.length === 0 ? <Shimmer/>:(
    <div className="body">
      <div className="filter">
        <div className="search">
            <input type="text" placeholder="search for restaurants" value = {searchText} 
            onChange = {(e) => setSearchText(e.target.value)}/>
            <button 
            onClick = {() => 
              
            {
              
              setRestaurants(filteredRestaurants.filter((restaurant => restaurant.card.card.info.name.toLowerCase().includes(searchText.toLowerCase()))))
            }
            }
            > Search
            </button>
        </div>
        <button className="filter-btn" onClick={() => {
          const filteredRestaurants = restaurants.filter((restaurant) => restaurant.card.card.info.avgRating > 4.5);
          setRestaurants(filteredRestaurants)
          }
          }>
          Top Rated Restaurants
        </button>
      </div>
      <div className="body-cards">
        {
          restaurants?.map((restaurant) => (<RestaurantCard key = {restaurant.card.card.info.id} resData={restaurant} />))
        }
      </div>
    </div>
  );
};

export default Body;
