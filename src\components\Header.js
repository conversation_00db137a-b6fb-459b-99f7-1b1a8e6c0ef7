import {APP_LOGO} from "../utils/constants";
import {useState,useEffect} from "react";
export const Header = () => {
    const [loggedInBtnState, setLoggedInBtnState] = useState("Login")
    useEffect (() => {
        console.log("use effect in header called")
    });
    console.log("Header rendered")
    return (
    <div  className = "header">
        <div className = "logo-container">
        <img className = "logo" src = {APP_LOGO}></img>
        </div>
        <div className="nav-items">
            <ul className = "list-items">
                <li>Home</li>
                <li>About Us</li>
                <li>Contact Us</li>
                <li>Cart</li>
                <button className="login-btn" onClick={() => {
                     console.log(loggedInBtnState)
                     loggedInBtnState === "Login" ? setLoggedInBtnState("Logout") : setLoggedInBtnState("Login")}
                     }> {loggedInBtnState} </button>
            </ul>

        </div>
    </div>
    );
}

export default Header;