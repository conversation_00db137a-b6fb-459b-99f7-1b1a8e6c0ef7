{"name": "namaste-react-handson", "version": "1.0.0", "description": "hands-on-with-react", "scripts": {"test": "jest", "start": "parcel index.html", "clearCacheAndRun": "rd /s /q .parcel-cache && rd /s /q dist && parcel index.html", "above start command": "runs the project in dev mode so that you can check development that you are making.", "build": "parcel build index.html"}, "repository": {"type": "git", "url": "git+https://github.com/saivinil/namaste-react-handson.git"}, "keywords": ["sai", "vinil", "<PERSON><PERSON><PERSON><PERSON>", "shakti-react"], "author": "vinil", "license": "ISC", "bugs": {"url": "https://github.com/saivinil/namaste-react-handson/issues"}, "homepage": "https://github.com/saivinil/namaste-react-handson#readme", "dependencies": {"parcel": "^2.13.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.8.1"}, "devDependencies": {"process": "^0.11.10"}, "browserslist": ["last 2 versions"]}