.header {
    display: flex;
    justify-content: space-between;  /* This will space out logo and nav items */
    align-items: center;            /* This vertically centers the items */
    border: 1px solid black;
}

.logo {
    width: 200px;
}

.list-items {
    display: flex;
    flex-direction: row;
    gap: 20px;                    /* Adds space between list items */
    list-style-type: none;        /* Removes bullet points */
    padding: 0;                   /* Removes default padding */
    margin: 0;                    /* Removes default margin */
}

.list-items li {
    padding: 10px;               /* Adds padding around each list item */
}

.nav-items {
    padding: 0 20px;             /* Adds some padding on sides */
}

.body {
    padding: 20px;
}

.body-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);  /* Creates 4 equal columns */
    gap: 20px;  /* Adds space between cards */
    padding: 20px;  /* Adds padding around the grid */
    max-width: 1200px;  /* Optional: limits maximum width */
    margin: 0 auto;  /* Optional: centers the grid */
}

.body-card {
    padding: 10px;
    border: 1px solid #ddd;  /* Optional: adds border around each card */
    border-radius: 8px;  /* Optional: rounds corners */
}

.body-card img {
    width: 100%;  /* Makes image fill the card */
    height: auto;  /* Maintains aspect ratio */
    border-radius: 4px;  /* Optional: rounds image corners */
}

.body-card:hover {
    border: 1px solid black;
    cursor: pointer;
    transform: scale(1.02); /* Optional: adds a slight zoom effect */
    transition: all 0.2s ease; /* Optional: makes the hover effect smooth */
}

/* Premium styling for filter button */
.filter {
    /* margin: 20px; */
    padding: 0 20px;
    display: flex;
}

.filter-btn {
    margin: 20px;
    cursor: pointer;
}

.search{
    margin: 20px;
}

.shimmer-container {
    display: flex;
    flex-wrap: wrap;
}

.shimmer-card {
    padding: 100px;
    margin: 0 auto;  /* Optional: centers the grid */
    border: 1px solid #ddd;  /* Optional: adds border around each card */
    border-radius: 8px;  /* Optional: rounds corners */
    background-color: lightgray;
    /* width: 200px;
    height: 400px; */
}

.shimmer-card:hover {
    border: 1px solid black;
    cursor: pointer;
    transform: scale(1.02); /* Optional: adds a slight zoom effect */
    transition: all 0.2s ease; /* Optional: makes the hover effect smooth */
}

.login-btn {
    padding: 10px;
    border: 1px solid #ddd;  /* Optional: adds border around each card */
    border-radius: 8px;  /* Optional: rounds corners */
    cursor: pointer;
}