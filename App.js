import React from "react"
import { createRoot } from 'react-dom/client';

import Header from "./src/components/Header";
import {Body} from "./src/components/Body";
import {createBrowserRouter, RouterProvider} from "react-router-dom";

const AppComponent = () => {
    return (
        <div id = "app" className = "app">
            <Header />
            <Body />
        </div>
    );
}

const appRouter = () => {[
    {
        path: "/",
        element: <AppComponent/>
    },
    {
        path: "/about",
        element: <About/>
    }
]}

const root = createRoot(document.getElementById('root'));
root.render(<AppComponent />);
